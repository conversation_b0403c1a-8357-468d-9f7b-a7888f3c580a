import{A as x,a as f,f as u,q as I,u as v,z as S}from"./chunk-TV52TPWK.js";import{E as p,I as t,J as o,K as i,O as a,Xa as s,db as b,eb as d,ib as h,p as m,pb as c,s as l}from"./chunk-DVLCYDDQ.js";import"./chunk-MYOBYLQN.js";import"./chunk-64QR6YHT.js";import"./chunk-6DBQW3S3.js";import"./chunk-MMQ4D4FU.js";import"./chunk-JZIOZHPP.js";import"./chunk-R7ZYQ4ZY.js";import"./chunk-4U6PRYVA.js";import"./chunk-F33KLCOC.js";import"./chunk-JWIEPCRG.js";import"./chunk-C5RQ2IC2.js";import"./chunk-D4VGNZLR.js";import"./chunk-JHLW7V75.js";import"./chunk-SV7S5NYR.js";import"./chunk-OLRFWS6T.js";var y=(()=>{let n=class n{constructor(){this.environmentInjector=m(l),f({home:I,camera:u,list:v,map:S,person:x})}};n.\u0275fac=function(r){return new(r||n)},n.\u0275cmp=p({type:n,selectors:[["app-tabs"]],decls:22,vars:0,consts:[["slot","bottom"],["tab","home","href","/tabs/home"],["aria-hidden","true","name","home"],["tab","identify","href","/tabs/identify"],["aria-hidden","true","name","camera"],["tab","observations","href","/tabs/observations"],["aria-hidden","true","name","list"],["tab","map","href","/tabs/map"],["aria-hidden","true","name","map"],["tab","profile","href","/tabs/profile"],["aria-hidden","true","name","person"]],template:function(r,g){r&1&&(t(0,"ion-tabs")(1,"ion-tab-bar",0)(2,"ion-tab-button",1),i(3,"ion-icon",2),t(4,"ion-label"),a(5,"Home"),o()(),t(6,"ion-tab-button",3),i(7,"ion-icon",4),t(8,"ion-label"),a(9,"Identify"),o()(),t(10,"ion-tab-button",5),i(11,"ion-icon",6),t(12,"ion-label"),a(13,"My Plants"),o()(),t(14,"ion-tab-button",7),i(15,"ion-icon",8),t(16,"ion-label"),a(17,"Explore"),o()(),t(18,"ion-tab-button",9),i(19,"ion-icon",10),t(20,"ion-label"),a(21,"Profile"),o()()()())},dependencies:[h,b,d,c,s],encapsulation:2});let e=n;return e})();var L=[{path:"",component:y,children:[{path:"home",loadComponent:()=>import("./chunk-MUQWNKBE.js").then(e=>e.HomePage)},{path:"identify",loadComponent:()=>import("./chunk-2CXZY2MS.js").then(e=>e.IdentifyPage)},{path:"observations",loadComponent:()=>import("./chunk-LV5BA3XV.js").then(e=>e.ObservationsListPage)},{path:"map",loadComponent:()=>import("./chunk-63FG4UFQ.js").then(e=>e.MapPage)},{path:"profile",loadComponent:()=>import("./chunk-7W6BQP57.js").then(e=>e.ProfilePage)},{path:"",redirectTo:"/tabs/home",pathMatch:"full"}]}];export{L as routes};
